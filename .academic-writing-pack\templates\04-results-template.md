# 结果模板 (Results Template)

[[LLM: This template guides presentation of research findings objectively and clearly. Use appropriate figures and tables, ensure statistical significance is properly reported, and maintain factual tone without interpretation. Present results in logical order.]]

## 模板说明
- **用途**: 学术论文结果部分
- **核心原则**: 客观性、准确性、清晰性
- **内容要求**: 只呈现事实，不进行解释
- **组织方式**: 按重要性或逻辑顺序排列

[[LLM: Focus on presenting findings without interpretation. Save analysis and discussion for the Discussion section.]]

## 1. 结果呈现结构

### 1.1 主要发现优先
[[LLM: Present the most important findings first, followed by supporting or secondary results.]]

**组织原则**:
- 最重要的发现放在前面
- 按照研究目标的顺序呈现
- 从定量到定性结果
- 从主要到次要发现

### 1.2 逻辑顺序安排
**建议顺序**:
1. 基础特征描述
2. 主要实验结果
3. 统计分析结果
4. 次要发现
5. 补充数据

---

## 2. 基础特征描述

### 2.1 样本基本特征
[[LLM: Provide comprehensive description of study population or sample characteristics.]]

**模板结构**:
```
**样本基本情况**：
本研究共纳入{{total_sample_size}}个样本，其中{{group_1_description}}{{group_1_size}}个（{{group_1_percentage}}%），{{group_2_description}}{{group_2_size}}个（{{group_2_percentage}}%）。

**基本特征**：
- 年龄：{{age_description}}（平均{{mean_age}} ± {{sd_age}}岁）
- 性别：男性{{male_count}}例（{{male_percentage}}%），女性{{female_count}}例（{{female_percentage}}%）
- {{other_characteristics}}：{{characteristic_description}}

各组间基本特征比较差异无统计学意义（P > 0.05），具有可比性。
```

### 2.2 实验条件确认
[[LLM: Confirm that experimental conditions were maintained as planned.]]

**模板结构**:
```
**实验条件**：
所有实验均在{{experimental_conditions}}条件下进行。环境参数监测结果显示：温度{{temperature_range}}，湿度{{humidity_range}}，{{other_parameters}}均在设定范围内，满足实验要求。
```

---

## 3. 主要实验结果

### 3.1 核心指标结果
[[LLM: Present the primary outcome measures with appropriate statistical details.]]

**模板结构**:
```
**{{primary_outcome_name}}结果**：
{{group_1_name}}组的{{outcome_measure}}为{{group_1_result}}（{{confidence_interval_1}}），{{group_2_name}}组为{{group_2_result}}（{{confidence_interval_2}}）。两组比较，差异具有统计学意义（{{statistical_test}}，P = {{p_value}}）。

**效应量**：{{effect_size_description}}为{{effect_size_value}}（{{effect_size_ci}}），表明{{effect_interpretation}}。
```

**写作要点**:
- 提供具体的数值结果
- 包含置信区间
- 报告统计检验方法和P值
- 必要时报告效应量

### 3.2 次要指标结果
[[LLM: Present secondary outcomes in similar format but with less detail.]]

**模板结构**:
```
**次要指标结果**：
- {{secondary_outcome_1}}：{{result_1}}（P = {{p_value_1}}）
- {{secondary_outcome_2}}：{{result_2}}（P = {{p_value_2}}）
- {{secondary_outcome_3}}：{{result_3}}（P = {{p_value_3}}）
```

### 3.3 时间序列结果
[[LLM: For longitudinal studies, present results across time points clearly.]]

**模板结构**:
```
**时间变化趋势**：
基线时，两组{{outcome_measure}}无显著差异（P > 0.05）。{{time_point_1}}时，{{group_comparison_1}}；{{time_point_2}}时，{{group_comparison_2}}；{{time_point_3}}时，{{group_comparison_3}}。

重复测量方差分析显示，时间主效应{{time_effect}}，组间主效应{{group_effect}}，时间×组别交互效应{{interaction_effect}}。
```

---

## 4. 图表结果呈现

### 4.1 图片结果
[[LLM: Ensure figures are informative and self-explanatory. Provide detailed captions.]]

**图片模板**:
```
**图{{figure_number}}** {{figure_title}}

{{figure_description}}

图{{figure_number}}显示了{{main_finding_from_figure}}。从图中可以看出，{{specific_observations}}。{{statistical_significance_note}}。

注：{{figure_notes}}
```

**图片类型选择**:
- **柱状图**: 适用于分类数据比较
- **折线图**: 适用于时间序列数据
- **散点图**: 适用于相关性分析
- **箱线图**: 适用于分布比较
- **热图**: 适用于矩阵数据展示

### 4.2 表格结果
[[LLM: Design tables to be clear and comprehensive. Include all necessary statistical information.]]

**表格模板**:
```
**表{{table_number}}** {{table_title}}

| {{column_1}} | {{column_2}} | {{column_3}} | {{column_4}} | P值 |
|-------------|-------------|-------------|-------------|-----|
| {{row_1_data}} | {{row_1_data}} | {{row_1_data}} | {{row_1_data}} | {{p_value_1}} |
| {{row_2_data}} | {{row_2_data}} | {{row_2_data}} | {{row_2_data}} | {{p_value_2}} |

注：{{table_notes}}；{{statistical_method_note}}；{{significance_note}}
```

**表格设计要点**:
- 标题简洁明确
- 列标题清晰
- 数据对齐一致
- 包含统计信息
- 添加必要注释

---

## 5. 统计分析结果

### 5.1 描述性统计
[[LLM: Provide comprehensive descriptive statistics for all key variables.]]

**模板结构**:
```
**描述性统计结果**：
{{variable_1}}的均值为{{mean_1}} ± {{sd_1}}，中位数为{{median_1}}（{{iqr_1}}），范围为{{range_1}}。
{{variable_2}}的均值为{{mean_2}} ± {{sd_2}}，中位数为{{median_2}}（{{iqr_2}}），范围为{{range_2}}。

**数据分布**：
Shapiro-Wilk正态性检验结果显示，{{normality_test_results}}。
```

### 5.2 推断性统计
[[LLM: Report all statistical tests with appropriate details including test statistics, degrees of freedom, and p-values.]]

**模板结构**:
```
**假设检验结果**：
- {{test_1_description}}：{{test_statistic_1}} = {{value_1}}，df = {{df_1}}，P = {{p_value_1}}
- {{test_2_description}}：{{test_statistic_2}} = {{value_2}}，df = {{df_2}}，P = {{p_value_2}}
- {{test_3_description}}：{{test_statistic_3}} = {{value_3}}，df = {{df_3}}，P = {{p_value_3}}

**多重比较校正**：采用{{correction_method}}校正，校正后的显著性水平为{{corrected_alpha}}。
```

### 5.3 相关性分析
[[LLM: Present correlation analyses with appropriate correlation coefficients and significance levels.]]

**模板结构**:
```
**相关性分析结果**：
{{variable_x}}与{{variable_y}}之间存在{{correlation_strength}}相关性（r = {{correlation_coefficient}}，P = {{p_value}}）。
{{variable_x}}与{{variable_z}}之间的相关性不显著（r = {{correlation_coefficient_2}}，P = {{p_value_2}}）。

**偏相关分析**：控制{{control_variables}}后，{{partial_correlation_results}}。
```

### 5.4 回归分析结果
[[LLM: Present regression results with model fit statistics and coefficient interpretations.]]

**模板结构**:
```
**回归分析结果**：
建立的{{regression_type}}回归模型为：
{{regression_equation}}

模型拟合优度：R² = {{r_squared}}，调整R² = {{adjusted_r_squared}}，F = {{f_statistic}}，P < {{p_value}}。

**回归系数**：
- {{predictor_1}}：β = {{beta_1}}，SE = {{se_1}}，t = {{t_1}}，P = {{p_1}}
- {{predictor_2}}：β = {{beta_2}}，SE = {{se_2}}，t = {{t_2}}，P = {{p_2}}
```

---

## 6. 次要发现

### 6.1 意外发现
[[LLM: Report any unexpected findings that emerged during the study.]]

**模板结构**:
```
**意外发现**：
在数据分析过程中发现，{{unexpected_finding}}。进一步分析显示，{{additional_analysis_results}}。该发现提示{{implication_note}}。
```

### 6.2 亚组分析
[[LLM: Present subgroup analyses if conducted, with appropriate cautions about interpretation.]]

**模板结构**:
```
**亚组分析结果**：
按{{subgroup_criteria}}进行亚组分析，结果显示：
- {{subgroup_1}}：{{subgroup_1_results}}
- {{subgroup_2}}：{{subgroup_2_results}}
- {{subgroup_3}}：{{subgroup_3_results}}

亚组间比较：{{subgroup_comparison_results}}。
```

### 6.3 敏感性分析
[[LLM: Report sensitivity analyses to test robustness of findings.]]

**模板结构**:
```
**敏感性分析**：
为验证结果的稳健性，进行了以下敏感性分析：
1. {{sensitivity_analysis_1}}：{{results_1}}
2. {{sensitivity_analysis_2}}：{{results_2}}
3. {{sensitivity_analysis_3}}：{{results_3}}

敏感性分析结果表明，{{robustness_conclusion}}。
```

---

## 7. 数据质量报告

### 7.1 数据完整性
[[LLM: Report on data completeness and any missing data patterns.]]

**模板结构**:
```
**数据完整性**：
总体数据完整率为{{completeness_rate}}%。各变量缺失情况：
- {{variable_1}}：缺失{{missing_1}}例（{{missing_rate_1}}%）
- {{variable_2}}：缺失{{missing_2}}例（{{missing_rate_2}}%）

缺失数据分析显示{{missing_data_pattern}}，采用{{imputation_method}}处理。
```

### 7.2 异常值处理
[[LLM: Report on outlier detection and handling procedures.]]

**模板结构**:
```
**异常值处理**：
采用{{outlier_detection_method}}检测异常值，发现{{outlier_count}}个异常值。经核实，{{outlier_verification}}，最终{{outlier_handling_decision}}。
```

---

## 质量检查清单

### 内容完整性
- [ ] 所有主要结果都已呈现
- [ ] 统计信息完整（检验方法、统计量、P值）
- [ ] 图表编号和标题正确
- [ ] 置信区间和效应量已报告
- [ ] 样本量和基本特征已描述

### 准确性检查
- [ ] 数值计算准确无误
- [ ] 统计方法选择恰当
- [ ] P值报告格式正确
- [ ] 图表与文字描述一致
- [ ] 单位标注正确

### 客观性检查
- [ ] 避免主观解释和推测
- [ ] 使用客观描述性语言
- [ ] 不包含讨论性内容
- [ ] 如实报告所有结果
- [ ] 包含不显著的结果

### 清晰性检查
- [ ] 结果呈现逻辑清晰
- [ ] 图表清晰易读
- [ ] 统计术语使用规范
- [ ] 重点结果突出
- [ ] 避免冗余信息

## 常见问题与改进建议

### 结果解释过度
**问题**: 在结果部分加入过多解释和讨论
**改进**: 严格区分结果呈现和结果解释，解释留到讨论部分

### 统计信息不完整
**问题**: 缺少检验方法、统计量或置信区间
**改进**: 按照统计报告标准提供完整信息

### 图表质量不高
**问题**: 图表不清晰或信息不完整
**改进**: 提高图表质量，确保自解释性

### 重点不突出
**问题**: 主要结果和次要结果混在一起
**改进**: 按重要性排序，突出核心发现
